import { Divider } from "@/components/ui/divider";
import { type FormField } from "@/types/form.types";
import { FieldActions } from "./FieldActions";
import { FieldDescriptionInput } from "./FieldDescriptionInput";
import { FieldLabelInput } from "./FieldLabelInput";

interface EditModeProps {
  field: FormField;
  onUpdate: (property: keyof FormField, value: unknown) => void;
  onDuplicate: () => void;
  onDelete: () => void;
  fieldIcon?: React.ReactNode;
  fieldTypeName?: string;
  labelPlaceholder?: string;
  descriptionPlaceholder?: string;
  descriptionMultiline?: boolean;
  showRequiredToggle?: boolean;
  showDescriptionToggle?: boolean;
  children?: React.ReactNode;
}

/**
 * Shared editing mode component for form field elements.
 * Handles the common editing UI pattern with label, description, and actions.
 */
export function EditMode({
  field,
  onUpdate,
  onDuplicate,
  onDelete,
  fieldIcon,
  fieldTypeName,
  labelPlaceholder,
  descriptionPlaceholder,
  descriptionMultiline = false,
  showRequiredToggle = true,
  showDescriptionToggle = true,
  children,
}: EditModeProps) {
  return (
    <div className="space-y-4">
      <div className="space-y-3">
        {/* Label input */}
        <FieldLabelInput
          value={field.label}
          onChange={(value) => onUpdate("label", value)}
          placeholder={labelPlaceholder}
        />

        {/* Description input (conditional) */}
        {field.showDescription && (
          <FieldDescriptionInput
            value={field.description}
            onChange={(value) => onUpdate("description", value)}
            placeholder={descriptionPlaceholder}
            multiline={descriptionMultiline}
          />
        )}

        {/* Field-specific content */}
        {children}
      </div>

      <Divider />

      {/* Actions and toggles */}
      <FieldActions
        onDuplicate={onDuplicate}
        onDelete={onDelete}
        fieldIcon={fieldIcon}
        fieldTypeName={fieldTypeName}
        showDescription={field.showDescription}
        onToggleDescription={
          showDescriptionToggle
            ? () => onUpdate("showDescription", !field.showDescription)
            : undefined
        }
        required={field.required}
        onToggleRequired={
          showRequiredToggle
            ? () => onUpdate("required", !field.required)
            : undefined
        }
      />
    </div>
  );
}
