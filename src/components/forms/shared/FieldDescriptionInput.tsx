import { cn } from "@/utils/tailwind-helpers";
import { forwardRef } from "react";

interface FieldDescriptionInputProps {
  value?: string;
  onChange: (value: string) => void;
  placeholder?: string;
  multiline?: boolean;
  className?: string;
}

/**
 * Shared input component for field descriptions across all form elements.
 * Supports both single-line input and textarea for longer descriptions.
 */
export const FieldDescriptionInput = forwardRef<
  HTMLInputElement | HTMLTextAreaElement,
  FieldDescriptionInputProps
>(({ value, onChange, placeholder = "Enter a description", multiline = false, className }, ref) => {
  const baseClassName = cn(
    "w-full border-none p-0 text-gray-600 focus:ring-0",
    className
  );

  if (multiline) {
    return (
      <textarea
        ref={ref as React.RefObject<HTMLTextAreaElement>}
        className={baseClassName}
        placeholder={placeholder}
        value={value || ""}
        onChange={(e) => onChange(e.target.value)}
        rows={2}
      />
    );
  }

  return (
    <input
      ref={ref as React.RefObject<HTMLInputElement>}
      className={baseClassName}
      placeholder={placeholder}
      value={value || ""}
      onChange={(e) => onChange(e.target.value)}
    />
  );
});

FieldDescriptionInput.displayName = "FieldDescriptionInput";
