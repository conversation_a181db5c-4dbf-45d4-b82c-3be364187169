/**
 * Example demonstrating how useFieldActions simplifies element components
 * This file shows the before/after comparison and can be deleted after implementation
 */

import { type FormField } from "@/types/form.types";
import { useFormStore } from "@/stores/form.store";
import { useFieldActions } from "./use-field-actions";

// BEFORE: Complex element with prop drilling and duplicate logic (185+ lines)
interface OldElementProps {
  form: any;
  element: FormField;
  index?: number;
  selectedId?: string;
  setSelectedId: (fieldId: string) => void;
  setForm: (form: any) => void;
}

function OldHeadingElement({ element, selectedId, setSelectedId, form, setForm }: OldElementProps) {
  // 30+ lines of local state management
  // 20+ lines of debounced update logic
  // 15+ lines of duplicate logic
  // 10+ lines of delete logic
  // 100+ lines of JSX with complex event handlers
  
  const duplicateElement = () => {
    // 20+ lines of complex duplication logic that's repeated in every element
    console.log("Complex duplication logic...");
  };

  const deleteElement = () => {
    // 15+ lines of deletion logic that's repeated in every element
    console.log("Complex deletion logic...");
  };

  const updateLabelOrDescription = (e: any) => {
    // 30+ lines of property update logic that's repeated in every element
    console.log("Complex property update logic...");
  };

  const updateShowDescription = () => {
    // 15+ lines of toggle logic that's repeated in every element
    console.log("Complex toggle logic...");
  };

  const updateIsRequired = () => {
    // 15+ lines of toggle logic that's repeated in every element
    console.log("Complex toggle logic...");
  };

  return <div>Complex 185+ line component...</div>;
}

// AFTER: Simplified element using useFieldActions (45 lines)
interface NewElementProps {
  field: FormField;
}

function NewHeadingElement({ field }: NewElementProps) {
  const { selectedId } = useFormStore();
  const { 
    duplicate, 
    remove, 
    updateProperty, 
    selectField,
    // Enhanced convenience methods
    updateLabel,
    updateDescription,
    toggleRequired,
    toggleShowDescription
  } = useFieldActions(field.id);
  const isSelected = field.id === selectedId;

  return (
    <div onClick={selectField}>
      {isSelected ? (
        <div>
          <input
            value={field.label}
            onChange={(e) => updateLabel(e.target.value)} // Simplified!
          />
          {field.showDescription && (
            <textarea
              value={field.description}
              onChange={(e) => updateDescription(e.target.value)} // Simplified!
            />
          )}
          <label>
            <input
              type="checkbox"
              checked={field.required}
              onChange={toggleRequired} // One-liner!
            />
            Required
          </label>
          <label>
            <input
              type="checkbox"
              checked={field.showDescription}
              onChange={toggleShowDescription} // One-liner!
            />
            Show Description
          </label>
          <button onClick={duplicate}>Duplicate</button>
          <button onClick={remove}>Delete</button>
        </div>
      ) : (
        <div>
          <h2>{field.label}</h2>
          {field.showDescription && <p>{field.description}</p>}
        </div>
      )}
    </div>
  );
}

// Benefits demonstrated:
// ✅ 75% reduction in component size (185 lines → 45 lines)
// ✅ No prop drilling (6 props → 1 prop)
// ✅ No duplicate logic across elements
// ✅ Consistent behavior across all elements
// ✅ Single place to modify common actions
// ✅ Better type safety with store actions
// ✅ Convenience methods for common operations
// ✅ Eliminates 50+ lines of duplicate code from each element
